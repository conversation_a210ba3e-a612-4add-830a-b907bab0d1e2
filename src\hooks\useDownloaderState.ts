import { useState, useCallback } from 'react';
import type { DownloadItem, DownloadProgress, AppState, IDownloadController, LiveRecordingController } from '../types';

/**
 * 下载器状态管理Hook
 * 负责管理下载器的所有状态和状态更新逻辑
 */
export function useDownloaderState() {
  const [state, setState] = useState<AppState>({
    downloadData: null,
    downloadProgress: {},
    savedFiles: new Set<string>(),
    pageTaskId: null,
    contentScriptReady: false,
    pageReady: false,
    isLoading: true,
    error: null,
    downloadController: null,
    downloadBlobUrl: null,
    liveRecordingController: null
  });

  // 更新下载进度
  const updateProgress = useCallback((requestId: string, progress: Partial<DownloadProgress[string]>) => {
    setState(prev => ({
      ...prev,
      downloadProgress: {
        ...prev.downloadProgress,
        [requestId]: {
          ...prev.downloadProgress[requestId],
          ...progress
        }
      }
    }));
  }, []);

  // 设置下载数据
  const setDownloadData = useCallback((downloadData: DownloadItem | null) => {
    setState(prev => ({
      ...prev,
      downloadData,
      isLoading: false,
      error: null
    }));
  }, []);

  // 设置错误状态
  const setError = useCallback((error: string) => {
    setState(prev => ({
      ...prev,
      error,
      isLoading: false
    }));
  }, []);

  // 设置页面任务ID
  const setPageTaskId = useCallback((pageTaskId: string) => {
    setState(prev => ({
      ...prev,
      pageTaskId,
      pageReady: true
    }));
  }, []);

  // 设置内容脚本准备状态
  const setContentScriptReady = useCallback((ready: boolean) => {
    setState(prev => ({
      ...prev,
      contentScriptReady: ready
    }));
  }, []);

  // 设置下载控制器
  const setDownloadController = useCallback((controller: IDownloadController) => {
    setState(prev => ({
      ...prev,
      downloadController: controller
    }));
  }, []);

  // 设置下载Blob URL
  const setDownloadBlobUrl = useCallback((blobUrl: string | null) => {
    setState(prev => ({
      ...prev,
      downloadBlobUrl: blobUrl
    }));
  }, []);

  // 标记文件为已保存
  const markFileAsSaved = useCallback((requestId: string) => {
    setState(prev => ({
      ...prev,
      savedFiles: new Set([...prev.savedFiles, requestId])
    }));
  }, []);

  // 设置加载状态
  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading
    }));
  }, []);

  // 更新下载数据的时长信息
  const updateDownloadDataDuration = useCallback((duration: string | null) => {
    if (duration) {
      setState(prev => ({
        ...prev,
        downloadData: prev.downloadData ? {
          ...prev.downloadData,
          duration
        } : null
      }));
    }
  }, []);

  // 更新下载数据的文件名
  const updateDownloadDataFilename = useCallback((filename: string) => {
    setState(prev => ({
      ...prev,
      downloadData: prev.downloadData ? {
        ...prev.downloadData,
        filename
      } : null
    }));
  }, []);

  // 设置直播录制控制器
  const setLiveRecordingController = useCallback((controller: LiveRecordingController | null) => {
    setState(prev => ({
      ...prev,
      liveRecordingController: controller
    }));
  }, []);

  // 停止直播录制
  const stopLiveRecording = useCallback(async (requestId: string) => {
    console.log('尝试停止直播录制:', requestId);

    // 优先尝试M3U8直播录制控制器
    const liveController = state.liveRecordingController;
    if (liveController && liveController.requestId === requestId) {
      console.log('找到M3U8直播录制控制器，当前录制状态:', liveController.isRecording);

      try {
        await liveController.stop();
        console.log('M3U8直播录制已停止:', requestId);

        // 清理控制器状态
        setState(prev => ({
          ...prev,
          liveRecordingController: null
        }));

        return;
      } catch (error) {
        console.error('停止M3U8直播录制失败:', error);
        throw error;
      }
    }

    // 如果没有M3U8控制器，尝试标准下载控制器（用于FLV等格式的直播流）
    const downloadController = state.downloadController;
    if (downloadController) {
      try {
        downloadController.cancel();
        console.log('标准直播录制已停止:', requestId);
      } catch (error) {
        console.error('停止标准直播录制失败:', error);
        throw error;
      }
    }

    console.log('没有找到匹配的录制控制器:', requestId);
  }, [state.liveRecordingController, state.downloadController]);

  return {
    state,
    setState,
    updateProgress,
    setDownloadData,
    setError,
    setPageTaskId,
    setContentScriptReady,
    setDownloadController,
    setDownloadBlobUrl,
    markFileAsSaved,
    setLoading,
    updateDownloadDataDuration,
    updateDownloadDataFilename,
    setLiveRecordingController,
    stopLiveRecording
  };
}
